import pandas as pd
import glob
import os
import random
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
import argparse
import asyncio
from dotenv import load_dotenv
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
from tqdm.asyncio import tqdm
from tqdm import tqdm as sync_tqdm
import gc
import psutil
from typing import Dict, List, Optional, Tuple
import warnings

# Try to import GPU monitoring libraries
try:
    import pynvml

    PYNVML_AVAILABLE = True
except ImportError:
    PYNVML_AVAILABLE = False

try:
    from accelerate import Accelerator, init_empty_weights, load_checkpoint_and_dispatch
    from accelerate.utils import get_balanced_memory, infer_auto_device_map

    ACCELERATE_AVAILABLE = True
except ImportError:
    ACCELERATE_AVAILABLE = False

load_dotenv()


def get_gpu_info() -> Dict[str, any]:
    """Get comprehensive GPU information and setup."""
    gpu_info = {
        "cuda_available": torch.cuda.is_available(),
        "device_count": 0,
        "devices": [],
        "total_memory": 0,
        "cuda_visible_devices": os.environ.get("CUDA_VISIBLE_DEVICES", "all"),
    }

    if torch.cuda.is_available():
        gpu_info["device_count"] = torch.cuda.device_count()

        for i in range(gpu_info["device_count"]):
            device_props = torch.cuda.get_device_properties(i)
            device_info = {
                "id": i,
                "name": device_props.name,
                "total_memory": device_props.total_memory,
                "major": device_props.major,
                "minor": device_props.minor,
            }
            gpu_info["devices"].append(device_info)
            gpu_info["total_memory"] += device_props.total_memory

    return gpu_info


def log_gpu_memory_usage(prefix: str = "") -> None:
    """Log current GPU memory usage."""
    if not torch.cuda.is_available():
        return

    for i in range(torch.cuda.device_count()):
        allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
        reserved = torch.cuda.memory_reserved(i) / 1024**3  # GB
        total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB

        print(
            f"{prefix}GPU {i}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved, {total:.2f}GB total"
        )


def clear_gpu_memory() -> None:
    """Clear GPU memory cache."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()


def get_optimal_device_map(model_name: str, gpu_info: Dict) -> Optional[Dict]:
    """Get optimal device mapping for multi-GPU setup."""
    if not ACCELERATE_AVAILABLE or gpu_info["device_count"] <= 1:
        return None

    try:
        # Use accelerate to determine optimal device mapping
        with init_empty_weights():
            model = AutoModelForCausalLM.from_pretrained(
                model_name, torch_dtype=torch.bfloat16, trust_remote_code=True
            )

        # Get balanced memory allocation
        max_memory = get_balanced_memory(
            model,
            max_memory=None,
            no_split_module_classes=[
                "LlamaDecoderLayer",
                "MistralDecoderLayer",
                "Qwen2DecoderLayer",
            ],
            dtype=torch.bfloat16,
            low_zero=(gpu_info["device_count"] > 1),
        )

        device_map = infer_auto_device_map(
            model,
            max_memory=max_memory,
            no_split_module_classes=[
                "LlamaDecoderLayer",
                "MistralDecoderLayer",
                "Qwen2DecoderLayer",
            ],
            dtype=torch.bfloat16,
        )

        return device_map
    except Exception as e:
        print(f"Warning: Could not determine optimal device map: {e}")
        return None


def setup_model_for_generation(model, enable_memory_optimization: bool = True):
    """Setup model with memory optimizations."""
    if enable_memory_optimization:
        # Enable gradient checkpointing if available
        if hasattr(model, "gradient_checkpointing_enable"):
            try:
                model.gradient_checkpointing_enable()
                print("✓ Gradient checkpointing enabled")
            except Exception as e:
                print(f"Warning: Could not enable gradient checkpointing: {e}")

        # Set model to eval mode for inference
        model.eval()

        # Disable gradients for inference
        for param in model.parameters():
            param.requires_grad = False

    return model


async def generate_responses_async(
    client,
    model_name,
    thinking,
    formatted_prompts,
    max_tokens,
    temperature,
    semaphore,
    K=1,
):
    """
    Generates responses from the specified model for a batch of prompts.

    Args:
        client: API client instance for the model
        model_name: Name of the model to use
        formatted_prompts: Dictionary mapping prompt keys to message lists
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        semaphore: Asyncio semaphore for rate limiting
        K: Number of responses to generate per prompt (default=1)

    Returns:
        dict: Mapping of prompt keys to lists of generated responses
    """
    results = {}

    async def process_prompt(prompt_key, messages):
        """Processes a single prompt with retries and rate limiting."""
        retry_delay = 3
        max_retries = 7
        jitter = 0.1

        all_responses = []

        for _ in range(K):
            for attempt in range(max_retries):
                try:
                    async with semaphore:
                        # Model-specific API calls
                        if "claude" in model_name:
                            system_prompt = None
                            user_messages = messages.copy()
                            if messages and messages[0]["role"] == "system":
                                system_prompt = messages[0]["content"]
                                user_messages = messages[1:]

                            message = await client.messages.create(
                                model=model_name,
                                max_tokens=max_tokens,
                                system=system_prompt,
                                messages=user_messages,
                                temperature=temperature,
                            )
                        elif (
                            isinstance(client, dict)
                            and client.get("type") == "huggingface"
                        ):
                            # Handle Hugging Face models with optimizations
                            model = client["model"]
                            tokenizer = client["tokenizer"]
                            gpu_info = client.get("gpu_info", {})

                            # Use the tokenizer's chat template if available
                            try:
                                if "qwen3" in model_name.lower():
                                    if thinking:
                                        prompt = tokenizer.apply_chat_template(
                                            messages,
                                            tokenize=False,
                                            add_generation_prompt=True,
                                            enable_thinking=True,
                                        )
                                    else:
                                        prompt = tokenizer.apply_chat_template(
                                            messages,
                                            tokenize=False,
                                            add_generation_prompt=True,
                                            enable_thinking=False,
                                        )
                                else:
                                    # Apply the model's chat template
                                    prompt = tokenizer.apply_chat_template(
                                        messages,
                                        tokenize=False,
                                        add_generation_prompt=True,
                                    )
                            except Exception as e:
                                print(
                                    f"Warning: Could not apply chat template for {model_name}: {e}"
                                )
                                print("Falling back to manual formatting...")
                                # Fallback to manual formatting for models without chat templates
                                prompt = ""
                                for msg in messages:
                                    role = msg["role"]
                                    content = msg["content"]
                                    if role == "system":
                                        prompt += f"<s>[INST] {content} [/INST]"
                                    elif role == "user":
                                        prompt += f"<s>[INST] {content} [/INST]"
                                    elif role == "assistant":
                                        prompt += f" {content} </s>"

                                # If the last message is from user, add the assistant start token
                                if messages[-1]["role"] == "user":
                                    prompt += " "

                            # Process in event loop to avoid blocking
                            loop = asyncio.get_event_loop()

                            # Tokenize with optimizations
                            def tokenize_prompt():
                                inputs = tokenizer(
                                    prompt,
                                    return_tensors="pt",
                                    padding=True,
                                    truncation=True,
                                    max_length=4096,  # Reasonable max length
                                )

                                # Move to appropriate device
                                if hasattr(model, "device"):
                                    device = model.device
                                elif gpu_info.get("cuda_available", False):
                                    device = torch.device("cuda:0")
                                else:
                                    device = torch.device("cpu")

                                return {k: v.to(device) for k, v in inputs.items()}

                            inputs = await loop.run_in_executor(None, tokenize_prompt)

                            # Generate response with optimizations
                            def generate_response():
                                generation_kwargs = {
                                    **inputs,
                                    "max_new_tokens": max_tokens,
                                    "temperature": temperature,
                                    "do_sample": temperature > 0,
                                    "top_p": 0.95,
                                    "pad_token_id": tokenizer.pad_token_id
                                    or tokenizer.eos_token_id,
                                }

                                # Add memory-efficient generation settings
                                if gpu_info.get("device_count", 0) > 1:
                                    generation_kwargs.update(
                                        {
                                            "use_cache": True,
                                            "output_attentions": False,
                                            "output_hidden_states": False,
                                        }
                                    )

                                with torch.no_grad():  # Ensure no gradients
                                    outputs = model.generate(**generation_kwargs)

                                return outputs

                            outputs = await loop.run_in_executor(
                                None, generate_response
                            )

                            # Decode the response
                            def decode_response():
                                response_text = tokenizer.decode(
                                    outputs[0][inputs["input_ids"].shape[1] :],
                                    skip_special_tokens=True,
                                )
                                return response_text.strip()

                            response = await loop.run_in_executor(None, decode_response)

                            # Clear memory periodically
                            if attempt % 5 == 0:  # Every 5 attempts
                                clear_gpu_memory()
                        else:
                            if (
                                "gpt" in model_name
                                or "o1" in model_name
                                or "o3" in model_name
                            ):
                                # replace "system" with "developer"
                                if messages and messages[0]["role"] == "system":
                                    messages[0]["role"] = "developer"
                            completion = await client.chat.completions.create(
                                model=model_name,
                                messages=messages,
                                max_tokens=max_tokens,
                                temperature=temperature,
                            )

                    # Extract response based on model type
                    if "claude" in model_name:
                        response = message.content[0].text
                    elif (
                        isinstance(client, dict) and client.get("type") == "huggingface"
                    ):
                        # Response is already extracted above
                        pass
                    else:
                        response = completion.choices[0].message.content

                    all_responses.append(response)
                    break

                except Exception as e:
                    if "invalid_prompt" in str(e):
                        print(
                            f"Prompt violated content policy for prompt: {prompt_key}. Replacing with error message."
                        )
                        all_responses.append(
                            "[Error: Prompt violated content policies]"
                        )
                        break
                    elif attempt == max_retries - 1:
                        print(f"Max retries exceeded for prompt: {prompt_key}")
                        print(e)
                        all_responses.append(None)
                    else:
                        print(
                            f"Error occurred for prompt: {prompt_key}. Attempt {attempt + 1}/{max_retries}. Retrying in {retry_delay:.1f} seconds."
                        )
                        print(e)
                        jittered_delay = retry_delay * (
                            1 + jitter * (2 * random.random() - 1)
                        )
                        await asyncio.sleep(jittered_delay)
                        retry_delay *= 2

        results[prompt_key] = all_responses

    tasks = [
        process_prompt(key, messages) for key, messages in formatted_prompts.items()
    ]
    await asyncio.gather(*tasks)

    return results


def create_model_client(model_name: str, enable_optimizations: bool = True):
    """Create optimized model client with multi-GPU support."""
    if "claude" in model_name.lower():
        return AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
    elif (
        "gpt" in model_name.lower()
        or "o1" in model_name.lower()
        or "o3" in model_name.lower()
    ):
        return AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    else:
        print(f"Loading Hugging Face model: {model_name}")

        # Get GPU information
        gpu_info = get_gpu_info()
        print(f"CUDA Available: {gpu_info['cuda_available']}")
        print(f"GPU Count: {gpu_info['device_count']}")
        print(f"CUDA_VISIBLE_DEVICES: {gpu_info['cuda_visible_devices']}")

        if gpu_info["cuda_available"]:
            for device in gpu_info["devices"]:
                print(
                    f"  GPU {device['id']}: {device['name']} ({device['total_memory'] / 1024**3:.1f}GB)"
                )

        # Clear any existing GPU memory
        clear_gpu_memory()

        # Load tokenizer
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

        # Ensure pad token is set
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        # Determine optimal device mapping
        device_map = None
        if enable_optimizations and gpu_info["device_count"] > 1:
            print("Determining optimal device mapping for multi-GPU setup...")
            device_map = get_optimal_device_map(model_name, gpu_info)
            if device_map:
                print(f"Using custom device map: {device_map}")
            else:
                print("Using automatic device mapping")
                device_map = "auto"
        else:
            device_map = "auto"

        # Load model with optimizations
        print("Loading model...")
        model_kwargs = {
            "torch_dtype": torch.bfloat16,
            "device_map": device_map,
            "trust_remote_code": True,
        }

        # Add memory optimization flags
        if enable_optimizations:
            model_kwargs.update(
                {
                    "low_cpu_mem_usage": True,
                    "use_cache": True,
                }
            )

        model = AutoModelForCausalLM.from_pretrained(model_name, **model_kwargs)

        # Apply additional optimizations
        if enable_optimizations:
            model = setup_model_for_generation(model, enable_memory_optimization=True)

        # Log memory usage after loading
        log_gpu_memory_usage("After model loading - ")

        return {
            "model": model,
            "tokenizer": tokenizer,
            "type": "huggingface",
            "gpu_info": gpu_info,
            "device_map": device_map,
        }


async def process_dataframe(
    input_csv,
    output_csv,
    temperature,
    model_name,
    thinking,
    max_tokens,
    lie_k,
    belief_k=None,
):
    """
    Process a dataframe and generate responses from an AI model.

    Args:
        input_csv (str): Path to input CSV file
        output_csv (str): Path to output CSV file
        temperature (float): Sampling temperature for generation
        model_name (str): Name of the model to use
        max_tokens (int): Maximum tokens for generation
        belief_k (dict): Required mapping of belief elicitation numbers to number of runs
            e.g. {"1": 3, "2": 1, "3": 1} means run belief_elicit_1 three times,
            belief_elicit_2 and belief_elicit_3 once each
        lie_k (int): Number of lying prompts to generate for each row

    The function reads a CSV containing prompts, generates responses using the specified
    model, and saves the results to a new CSV file.
    """
    client = None
    try:
        # Initialize model-specific client
        client = create_model_client(model_name)

        semaphore = asyncio.Semaphore(10)

        # Read input data and set default system prompt if needed
        df = pd.read_csv(input_csv)
        if (
            "belief_system_1" not in df.columns and "belief_elicit_1" in df.columns
        ):  # All files except provided_facts and statistics
            df["belief_system_1"] = "You are a helpful assistant."

        # Check if this is a doubling down scenario (multi-turn conversation)
        is_doubling_down = (
            "dd_turn_1_prompt" in df.columns and "dd_turn_1_response" in df.columns
        )
        print(f"Is this a doubling down scenario? {is_doubling_down}")

        # Prepare prompts for generation
        all_prompts = []
        indices = []
        prompt_types = []

        # Process each row in the dataframe
        print(f"Processing {len(df)} rows from dataframe...")
        for index, row in sync_tqdm(
            df.iterrows(), total=len(df), desc="Processing rows"
        ):
            # Handle lying prompts - either doubling down or standard
            if is_doubling_down:
                # For doubling down, include the full conversation history
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["dd_turn_1_prompt"]},
                            {"role": "assistant", "content": row["dd_turn_1_response"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")
            else:
                # Standard lying prompt with just system and user messages
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")

            # Handle belief elicitation prompts if present
            if "belief_elicit_1" in df.columns:
                for be_num, k_value in belief_k.items():
                    belief_col = f"belief_elicit_{be_num}"
                    if pd.notna(row.get(belief_col)):
                        for run in range(k_value):
                            all_prompts.append(
                                [
                                    {
                                        "role": "system",
                                        "content": row["belief_system_1"],
                                    },
                                    {"role": "user", "content": row[belief_col]},
                                ]
                            )
                            indices.append(index)
                            prompt_types.append(f"be{be_num}_run{run+1}")

        # Generate responses in parallel for all prompts with progress bar
        print(f"Generating responses for {len(all_prompts)} prompts...")

        tasks = [
            generate_responses_async(
                client,
                model_name,
                thinking,
                {prompt_type: prompt},
                max_tokens=max_tokens,
                temperature=temperature,
                semaphore=semaphore,
                K=1,
            )
            for prompt, prompt_type in zip(all_prompts, prompt_types)
        ]

        all_responses = await tqdm.gather(
            *tasks, desc="Generating responses", total=len(tasks)
        )

        # Create mapping for output column names
        column_mapping = {
            f"lying_run{run+1}": f"generation(System Prompt + User Prompt)_run{run+1}"
            for run in range(lie_k)
        }

        # Add belief elicitation column names if present
        if "belief_elicit_1" in df.columns:
            for be_num, k_value in belief_k.items():
                for run in range(k_value):
                    column_mapping[f"be{be_num}_run{run+1}"] = (
                        f"generation(belief_system_1 + belief_elicit_{be_num})_run{run+1}"
                    )

        # Process responses and save to dataframe
        print("Processing responses and saving to dataframe...")
        for response, index, prompt_type in sync_tqdm(
            zip(all_responses, indices, prompt_types),
            total=len(all_responses),
            desc="Processing responses",
        ):
            column_name = column_mapping[prompt_type]
            try:
                response_value = (
                    response[prompt_type][0] if prompt_type in response else None
                )
                if response_value is None:
                    raise ValueError("Generation failed or timed out")
                df.at[index, column_name] = response_value
            except Exception as e:
                error_msg = f"[ERROR: {str(e)}]"
                print(
                    f"WARNING: Error processing response for index {index}, prompt type {prompt_type}: {str(e)}"
                )
                df.at[index, column_name] = error_msg

        # Save results to CSV
        df.to_csv(output_csv, index=False)

    finally:
        # Clean up client if needed
        if hasattr(client, "close"):
            await client.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate responses from AI models for a set of prompts with GPU optimizations"
    )
    parser.add_argument("--test", action="store_true", help="Run in test mode")
    parser.add_argument(
        "--model",
        default="meta-llama/Llama-3.1-8B-Instruct",
        help="Model name to use for generation",
    )
    parser.add_argument(
        "--thinking",
        action="store_true",
        help="Enable thinking mode if applicable",
    )
    parser.add_argument(
        "--max_tokens", type=int, default=1000, help="Maximum tokens for generation"
    )
    parser.add_argument(
        "--temperature", type=float, default=1.0, help="Temperature for generation"
    )
    parser.add_argument(
        "--lie_k",
        type=int,
        default=1,
        help="Number of lying prompts to generate for each row",
    )
    parser.add_argument(
        "--disable_optimizations",
        action="store_true",
        help="Disable GPU memory optimizations and multi-GPU features",
    )
    parser.add_argument(
        "--memory_monitoring",
        action="store_true",
        help="Enable detailed GPU memory monitoring during generation",
    )
    parser.add_argument(
        "--batch_size",
        type=int,
        default=1,
        help="Batch size for generation (experimental, use with caution)",
    )

    args = parser.parse_args()

    # Print optimization settings
    print("=== GPU Optimization Settings ===")
    print(f"Optimizations enabled: {not args.disable_optimizations}")
    print(f"Memory monitoring: {args.memory_monitoring}")
    print(f"Batch size: {args.batch_size}")
    print("=" * 35)

    for file in glob.iglob("test_csv_data/*.csv" if args.test else "csv_data/*.csv"):
        belief_k = {"1": 3} if "statistics" in file else {"1": 3, "2": 1, "3": 1}

        modelname = args.model.split("/")[-1] if "/" in args.model else args.model
        output_file = file.replace(".csv", f"_{modelname}.csv")
        output_file = output_file.replace("csv_data/", "csv_data/responses/")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Skip if output file already exists
        if os.path.exists(output_file):
            print(f"Skipping {output_file} as it already exists")
            continue

        print(f"\nProcessing {file} -> {output_file}")

        # Store optimization settings globally for access in other functions
        global ENABLE_OPTIMIZATIONS, MEMORY_MONITORING, BATCH_SIZE
        ENABLE_OPTIMIZATIONS = not args.disable_optimizations
        MEMORY_MONITORING = args.memory_monitoring
        BATCH_SIZE = args.batch_size

        asyncio.run(
            process_dataframe(
                file,
                output_file,
                temperature=args.temperature,
                model_name=args.model,
                thinking=args.thinking,
                max_tokens=args.max_tokens,
                lie_k=args.lie_k,
                belief_k=belief_k,
            )
        )

        # Clear memory between files
        if ENABLE_OPTIMIZATIONS:
            clear_gpu_memory()
            print("Cleared GPU memory between files")
