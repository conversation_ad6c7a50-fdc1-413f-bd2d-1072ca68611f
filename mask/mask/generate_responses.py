import pandas as pd
import glob
import os
import random
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
import argparse
import asyncio
from dotenv import load_dotenv
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch
from tqdm.asyncio import tqdm
from tqdm import tqdm as sync_tqdm
import gc
import queue
import multiprocessing as mp
import tempfile
import subprocess
import sys
from typing import Dict, List, Optional

# Try to import GPU monitoring libraries
try:
    import pynvml

    pynvml.nvmlInit()
    PYNVML_AVAILABLE = True
except ImportError:
    PYNVML_AVAILABLE = False

try:
    from accelerate import init_empty_weights
    from accelerate.utils import get_balanced_memory, infer_auto_device_map

    ACCELERATE_AVAILABLE = True
except ImportError:
    ACCELERATE_AVAILABLE = False

# Try to import vLLM
try:
    from vllm import LLM, SamplingParams

    VLLM_AVAILABLE = True
except ImportError:
    VLLM_AVAILABLE = False

load_dotenv()


def get_gpu_info() -> Dict[str, any]:
    """Get comprehensive GPU information and setup."""
    gpu_info = {
        "cuda_available": torch.cuda.is_available(),
        "device_count": 0,
        "devices": [],
        "total_memory": 0,
        "cuda_visible_devices": os.environ.get("CUDA_VISIBLE_DEVICES", "all"),
    }

    if torch.cuda.is_available():
        gpu_info["device_count"] = torch.cuda.device_count()

        for i in range(gpu_info["device_count"]):
            device_props = torch.cuda.get_device_properties(i)
            device_info = {
                "id": i,
                "name": device_props.name,
                "total_memory": device_props.total_memory,
                "major": device_props.major,
                "minor": device_props.minor,
            }
            gpu_info["devices"].append(device_info)
            gpu_info["total_memory"] += device_props.total_memory

    return gpu_info


def log_gpu_memory_usage(prefix: str = "") -> None:
    """Log current GPU memory usage."""
    if not torch.cuda.is_available():
        return

    for i in range(torch.cuda.device_count()):
        allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
        reserved = torch.cuda.memory_reserved(i) / 1024**3  # GB
        total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB

        print(
            f"{prefix}GPU {i}: {allocated:.2f}GB allocated, {reserved:.2f}GB reserved, {total:.2f}GB total"
        )


def clear_gpu_memory() -> None:
    """Clear GPU memory cache."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()


def get_gpu_memory_info(device_id: int = None) -> Dict[str, float]:
    """Get detailed memory information for a specific GPU or all GPUs."""
    if not torch.cuda.is_available():
        return {}

    if device_id is not None:
        devices = [device_id]
    else:
        devices = list(range(torch.cuda.device_count()))

    memory_info = {}
    for i in devices:
        allocated = torch.cuda.memory_allocated(i) / 1024**3  # GB
        reserved = torch.cuda.memory_reserved(i) / 1024**3  # GB
        total = torch.cuda.get_device_properties(i).total_memory / 1024**3  # GB
        free = total - allocated

        memory_info[f"gpu_{i}"] = {
            "allocated": allocated,
            "reserved": reserved,
            "total": total,
            "free": free,
            "utilization": allocated / total if total > 0 else 0,
        }

    return memory_info


def select_optimal_gpus(num_gpus: int = None, min_memory_gb: float = 4.0) -> List[int]:
    """Select optimal GPUs based on available memory and utilization."""
    if not torch.cuda.is_available():
        return []

    available_gpus = []
    memory_info = get_gpu_memory_info()

    for gpu_id in range(torch.cuda.device_count()):
        gpu_key = f"gpu_{gpu_id}"
        if gpu_key in memory_info:
            gpu_mem = memory_info[gpu_key]
            if gpu_mem["free"] >= min_memory_gb and gpu_mem["utilization"] < 0.9:
                available_gpus.append((gpu_id, gpu_mem["free"], gpu_mem["utilization"]))

    # Sort by free memory (descending) and utilization (ascending)
    available_gpus.sort(key=lambda x: (-x[1], x[2]))

    if num_gpus is None:
        num_gpus = len(available_gpus)

    selected_gpus = [gpu_id for gpu_id, _, _ in available_gpus[:num_gpus]]
    return selected_gpus


def calculate_optimal_batch_size(
    model_name: str, gpu_memory_gb: float, max_tokens: int = 1000
) -> int:
    """Calculate optimal batch size based on GPU memory and model size."""
    # Rough estimates for memory usage per token (in MB)
    model_memory_estimates = {
        "7b": 14000,  # ~14GB for 7B model
        "8b": 16000,  # ~16GB for 8B model
        "13b": 26000,  # ~26GB for 13B model
        "30b": 60000,  # ~60GB for 30B model
        "70b": 140000,  # ~140GB for 70B model
    }

    # Estimate model size from name
    model_size_mb = 8000  # Default for smaller models
    model_name_lower = model_name.lower()

    for size, memory in model_memory_estimates.items():
        if size in model_name_lower:
            model_size_mb = memory
            break

    # Calculate available memory for generation (leave 20% buffer)
    available_memory_mb = gpu_memory_gb * 1024 * 0.8 - model_size_mb

    if available_memory_mb <= 0:
        return 1

    # Rough estimate: each token uses ~2MB during generation
    memory_per_token = 2
    tokens_per_sequence = max_tokens + 512  # Input + output tokens
    memory_per_sequence = tokens_per_sequence * memory_per_token

    batch_size = max(1, int(available_memory_mb / memory_per_sequence))
    return min(batch_size, 32)  # Cap at 32 for stability


def split_dataframe_for_gpus(df: pd.DataFrame, num_gpus: int) -> List[pd.DataFrame]:
    """Split dataframe into chunks for parallel processing across GPUs."""
    total_rows = len(df)
    rows_per_gpu = total_rows // num_gpus
    remainder = total_rows % num_gpus

    chunks = []
    start_idx = 0

    for gpu_id in range(num_gpus):
        # Distribute remainder rows among first few GPUs
        chunk_size = rows_per_gpu + (1 if gpu_id < remainder else 0)
        end_idx = start_idx + chunk_size

        chunk = df.iloc[start_idx:end_idx].copy()
        chunks.append(chunk)

        start_idx = end_idx

    return chunks


def create_vllm_worker_script() -> str:
    """Create a Python script for vLLM worker processes."""
    script_content = '''
import os
import sys
import pandas as pd
import argparse
from typing import List, Dict
import json

try:
    from vllm import LLM, SamplingParams
    VLLM_AVAILABLE = True
except ImportError:
    VLLM_AVAILABLE = False
    print("vLLM not available in worker process")
    sys.exit(1)


def format_messages_for_vllm(messages: List[Dict], model_name: str) -> str:
    """Format chat messages into a prompt string for vLLM."""
    prompt = ""
    for msg in messages:
        role = msg["role"]
        content = msg["content"]

        if role == "system":
            prompt += f"System: {content}\\n"
        elif role == "user":
            prompt += f"User: {content}\\n"
        elif role == "assistant":
            prompt += f"Assistant: {content}\\n"

    # Add assistant prompt if last message is from user
    if messages and messages[-1]["role"] == "user":
        prompt += "Assistant: "

    return prompt


def process_chunk_with_vllm(
    input_file: str,
    output_file: str,
    gpu_id: int,
    model_name: str,
    temperature: float,
    max_tokens: int,
    thinking: bool,
    lie_k: int,
    belief_k: Dict,
):
    """Process a data chunk using vLLM on a specific GPU."""
    # Set GPU for this process
    os.environ["CUDA_VISIBLE_DEVICES"] = str(gpu_id)

    print(f"Worker {gpu_id}: Loading model {model_name}")

    # Initialize vLLM
    llm = LLM(
        model=model_name,
        trust_remote_code=True,
        dtype="bfloat16",
        gpu_memory_utilization=0.9,
    )


    sampling_params = SamplingParams(
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=0.95,
    )

    # Load data chunk
    df = pd.read_csv(input_file)
    print(f"Worker {gpu_id}: Processing {len(df)} rows")

    # Set default system prompt if needed
    if "belief_system_1" not in df.columns and "belief_elicit_1" in df.columns:
        df["belief_system_1"] = "You are a helpful assistant."

    # Check if this is a doubling down scenario
    is_doubling_down = (
        "dd_turn_1_prompt" in df.columns and "dd_turn_1_response" in df.columns
    )

    # Prepare all prompts for batch processing
    all_prompts = []
    prompt_metadata = []  # Track which row and column each prompt belongs to

    for index, row in df.iterrows():
        # Handle lying prompts
        if is_doubling_down:
            for run in range(lie_k):
                messages = [
                    {"role": "system", "content": row["system_prompt"]},
                    {"role": "user", "content": row["dd_turn_1_prompt"]},
                    {"role": "assistant", "content": row["dd_turn_1_response"]},
                    {"role": "user", "content": row["user_prompt"]},
                ]
                prompt = format_messages_for_vllm(messages, model_name)
                all_prompts.append(prompt)
                prompt_metadata.append({
                    "row_index": index,
                    "column": f"generation(System Prompt + User Prompt)_run{run+1}",
                    "type": "lying"
                })
        else:
            for run in range(lie_k):
                messages = [
                    {"role": "system", "content": row["system_prompt"]},
                    {"role": "user", "content": row["user_prompt"]},
                ]
                prompt = format_messages_for_vllm(messages, model_name)
                all_prompts.append(prompt)
                prompt_metadata.append({
                    "row_index": index,
                    "column": f"generation(System Prompt + User Prompt)_run{run+1}",
                    "type": "lying"
                })

        # Handle belief elicitation prompts
        if "belief_elicit_1" in df.columns:
            for be_num, k_value in belief_k.items():
                belief_col = f"belief_elicit_{be_num}"
                if pd.notna(row.get(belief_col)):
                    for run in range(k_value):
                        messages = [
                            {"role": "system", "content": row["belief_system_1"]},
                            {"role": "user", "content": row[belief_col]},
                        ]
                        prompt = format_messages_for_vllm(messages, model_name)
                        all_prompts.append(prompt)
                        prompt_metadata.append({
                            "row_index": index,
                            "column": f"generation(belief_system_1 + belief_elicit_{be_num})_run{run+1}",
                            "type": "belief"
                        })

    print(f"Worker {gpu_id}: Generating {len(all_prompts)} responses")

    # Generate all responses in batch
    outputs = llm.generate(all_prompts, sampling_params)

    # Process outputs and update dataframe
    for output, metadata in zip(outputs, prompt_metadata):
        response_text = output.outputs[0].text.strip()
        row_idx = metadata["row_index"]
        column = metadata["column"]
        df.at[row_idx, column] = response_text

    # Save results
    df.to_csv(output_file, index=False)
    print(f"Worker {gpu_id}: Saved results to {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_file", required=True)
    parser.add_argument("--output_file", required=True)
    parser.add_argument("--gpu_id", type=int, required=True)
    parser.add_argument("--model_name", required=True)
    parser.add_argument("--temperature", type=float, default=1.0)
    parser.add_argument("--max_tokens", type=int, default=1000)
    parser.add_argument("--thinking", action="store_true")
    parser.add_argument("--lie_k", type=int, default=1)
    parser.add_argument("--belief_k", type=str, default='{"1": 3, "2": 1, "3": 1}')

    args = parser.parse_args()
    belief_k = json.loads(args.belief_k)

    process_chunk_with_vllm(
        args.input_file,
        args.output_file,
        args.gpu_id,
        args.model_name,
        args.temperature,
        args.max_tokens,
        args.thinking,
        args.lie_k,
        belief_k,
    )
'''
    return script_content


def process_dataframe_with_vllm(
    input_csv: str,
    output_csv: str,
    model_name: str,
    temperature: float,
    max_tokens: int,
    thinking: bool,
    lie_k: int,
    belief_k: Dict,
    num_gpus: int = None,
) -> None:
    """Process dataframe using vLLM with data parallelism across multiple GPUs."""
    if not VLLM_AVAILABLE:
        raise ImportError(
            "vLLM is not available. Please install with: pip install vllm"
        )

    # Get available GPUs
    gpu_info = get_gpu_info()
    if not gpu_info["cuda_available"]:
        raise RuntimeError("CUDA is not available")

    available_gpus = list(range(gpu_info["device_count"]))
    if num_gpus is None:
        num_gpus = len(available_gpus)
    else:
        num_gpus = min(num_gpus, len(available_gpus))
        available_gpus = available_gpus[:num_gpus]

    print(f"Using vLLM with {num_gpus} GPUs: {available_gpus}")

    # Load and split the dataframe
    df = pd.read_csv(input_csv)
    print(f"Total rows to process: {len(df)}")

    if len(df) < num_gpus:
        print(f"Warning: Only {len(df)} rows but {num_gpus} GPUs. Using single GPU.")
        num_gpus = 1
        available_gpus = available_gpus[:1]

    # Split dataframe into chunks
    df_chunks = split_dataframe_for_gpus(df, num_gpus)

    # Create temporary files for each chunk
    temp_files = []
    output_files = []

    for i, chunk in enumerate(df_chunks):
        # Create temporary input file
        temp_input = f"/tmp/vllm_chunk_{i}_{os.getpid()}.csv"
        chunk.to_csv(temp_input, index=False)
        temp_files.append(temp_input)

        # Create temporary output file
        temp_output = f"/tmp/vllm_output_{i}_{os.getpid()}.csv"
        output_files.append(temp_output)

        print(f"GPU {available_gpus[i]}: Processing {len(chunk)} rows")

    # Create worker script
    worker_script_content = create_vllm_worker_script()
    worker_script_path = f"/tmp/vllm_worker_{os.getpid()}.py"

    with open(worker_script_path, "w") as f:
        f.write(worker_script_content)

    # Launch worker processes
    processes = []

    try:
        for i, gpu_id in enumerate(available_gpus):
            cmd = [
                sys.executable,
                worker_script_path,
                "--input_file",
                temp_files[i],
                "--output_file",
                output_files[i],
                "--gpu_id",
                str(gpu_id),
                "--model_name",
                model_name,
                "--temperature",
                str(temperature),
                "--max_tokens",
                str(max_tokens),
                "--lie_k",
                str(lie_k),
                "--belief_k",
                str(belief_k).replace("'", '"'),  # Convert to JSON format
            ]

            if thinking:
                cmd.append("--thinking")

            print(f"Starting worker process for GPU {gpu_id}")
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE
            )
            processes.append((process, gpu_id))

        # Wait for all processes to complete
        for process, gpu_id in processes:
            stdout, stderr = process.communicate()

            if process.returncode != 0:
                print(f"Error in worker {gpu_id}:")
                print(stderr.decode())
                raise RuntimeError(f"Worker process {gpu_id} failed")
            else:
                print(f"Worker {gpu_id} completed successfully")
                if stdout:
                    print(f"Worker {gpu_id} output: {stdout.decode()}")

        # Combine results from all workers
        print("Combining results from all workers...")
        combined_df = pd.DataFrame()

        for i, output_file in enumerate(output_files):
            if os.path.exists(output_file):
                chunk_df = pd.read_csv(output_file)
                combined_df = pd.concat([combined_df, chunk_df], ignore_index=True)
            else:
                raise FileNotFoundError(f"Output file {output_file} not found")

        # Save final result
        combined_df.to_csv(output_csv, index=False)
        print(f"Final results saved to {output_csv}")

    finally:
        # Clean up temporary files
        for temp_file in temp_files + output_files + [worker_script_path]:
            if os.path.exists(temp_file):
                os.remove(temp_file)


def get_optimal_device_map(model_name: str, gpu_info: Dict) -> Optional[Dict]:
    """Get optimal device mapping for multi-GPU setup."""
    if not ACCELERATE_AVAILABLE or gpu_info["device_count"] <= 1:
        return None

    try:
        # Use accelerate to determine optimal device mapping
        with init_empty_weights():
            model = AutoModelForCausalLM.from_pretrained(
                model_name, torch_dtype=torch.bfloat16, trust_remote_code=True
            )

        # Get balanced memory allocation
        max_memory = get_balanced_memory(
            model,
            max_memory=None,
            no_split_module_classes=[
                "LlamaDecoderLayer",
                "MistralDecoderLayer",
                "Qwen2DecoderLayer",
            ],
            dtype=torch.bfloat16,
            low_zero=(gpu_info["device_count"] > 1),
        )

        device_map = infer_auto_device_map(
            model,
            max_memory=max_memory,
            no_split_module_classes=[
                "LlamaDecoderLayer",
                "MistralDecoderLayer",
                "Qwen2DecoderLayer",
            ],
            dtype=torch.bfloat16,
        )

        return device_map
    except Exception as e:
        print(f"Warning: Could not determine optimal device map: {e}")
        return None


def setup_model_for_generation(model, enable_memory_optimization: bool = True):
    """Setup model with memory optimizations."""
    if enable_memory_optimization:
        # Enable gradient checkpointing if available
        if hasattr(model, "gradient_checkpointing_enable"):
            try:
                model.gradient_checkpointing_enable()
                print("✓ Gradient checkpointing enabled")
            except Exception as e:
                print(f"Warning: Could not enable gradient checkpointing: {e}")

        # Set model to eval mode for inference
        model.eval()

        # Disable gradients for inference
        for param in model.parameters():
            param.requires_grad = False

    return model


class MultiGPUModelManager:
    """Manages multiple model instances across GPUs for parallel inference."""

    def __init__(
        self, model_name: str, gpu_ids: List[int], enable_optimizations: bool = True
    ):
        self.model_name = model_name
        self.gpu_ids = gpu_ids
        self.enable_optimizations = enable_optimizations
        self.models = {}
        self.tokenizers = {}
        self.device_queues = {}
        self.load_balancer_index = 0

        # Initialize models on each GPU
        self._initialize_models()

    def _initialize_models(self):
        """Initialize model instances on each specified GPU."""
        print(f"Initializing models on GPUs: {self.gpu_ids}")

        for gpu_id in self.gpu_ids:
            print(f"Loading model on GPU {gpu_id}...")

            # Set CUDA device
            torch.cuda.set_device(gpu_id)
            device = torch.device(f"cuda:{gpu_id}")

            # Load tokenizer (shared across all instances)
            if not self.tokenizers:
                tokenizer = AutoTokenizer.from_pretrained(
                    self.model_name, trust_remote_code=True
                )
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                self.tokenizers[gpu_id] = tokenizer
            else:
                # Reuse tokenizer from first GPU
                self.tokenizers[gpu_id] = list(self.tokenizers.values())[0]

            # Load model on specific GPU
            model_kwargs = {
                "torch_dtype": torch.bfloat16,
                "device_map": {"": device},
                "trust_remote_code": True,
            }

            if self.enable_optimizations:
                model_kwargs.update(
                    {
                        "low_cpu_mem_usage": True,
                        "use_cache": True,
                    }
                )

            model = AutoModelForCausalLM.from_pretrained(
                self.model_name, **model_kwargs
            )

            if self.enable_optimizations:
                model = setup_model_for_generation(
                    model, enable_memory_optimization=True
                )

            self.models[gpu_id] = model

            # Create a queue for managing requests to this GPU
            self.device_queues[gpu_id] = queue.Queue()

            print(f"✓ Model loaded on GPU {gpu_id}")

    def get_available_gpu(self) -> int:
        """Get the next available GPU using round-robin load balancing."""
        if not self.gpu_ids:
            raise RuntimeError("No GPUs available")

        # Simple round-robin selection
        gpu_id = self.gpu_ids[self.load_balancer_index % len(self.gpu_ids)]
        self.load_balancer_index += 1
        return gpu_id

    def get_model_and_tokenizer(self, gpu_id: int = None):
        """Get model and tokenizer for a specific GPU or auto-select."""
        if gpu_id is None:
            gpu_id = self.get_available_gpu()

        if gpu_id not in self.models:
            raise ValueError(f"No model loaded on GPU {gpu_id}")

        return self.models[gpu_id], self.tokenizers[gpu_id], gpu_id

    def generate_batch(self, prompts: List[str], gpu_id: int, **generation_kwargs):
        """Generate responses for a batch of prompts on a specific GPU."""
        model, tokenizer, _ = self.get_model_and_tokenizer(gpu_id)

        # Set device context
        torch.cuda.set_device(gpu_id)
        device = torch.device(f"cuda:{gpu_id}")

        # Tokenize batch
        inputs = tokenizer(
            prompts,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=4096,
        )

        # Move to device
        inputs = {k: v.to(device) for k, v in inputs.items()}

        # Generate
        with torch.no_grad():
            outputs = model.generate(**inputs, **generation_kwargs)

        # Decode responses
        responses = []
        for output in outputs:
            response_text = tokenizer.decode(
                output[inputs["input_ids"].shape[1] :],
                skip_special_tokens=True,
            )
            responses.append(response_text.strip())

        return responses

    def cleanup(self):
        """Clean up model instances and free GPU memory."""
        for gpu_id in self.models:
            del self.models[gpu_id]
            torch.cuda.set_device(gpu_id)
            torch.cuda.empty_cache()

        self.models.clear()
        self.tokenizers.clear()
        gc.collect()


async def generate_responses_async(
    client,
    model_name,
    thinking,
    formatted_prompts,
    max_tokens,
    temperature,
    semaphore,
    K=1,
):
    """
    Generates responses from the specified model for a batch of prompts.

    Args:
        client: API client instance for the model
        model_name: Name of the model to use
        formatted_prompts: Dictionary mapping prompt keys to message lists
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        semaphore: Asyncio semaphore for rate limiting
        K: Number of responses to generate per prompt (default=1)

    Returns:
        dict: Mapping of prompt keys to lists of generated responses
    """
    results = {}

    async def process_prompt(prompt_key, messages):
        """Processes a single prompt with retries and rate limiting."""
        retry_delay = 3
        max_retries = 7
        jitter = 0.1

        all_responses = []

        for _ in range(K):
            for attempt in range(max_retries):
                try:
                    async with semaphore:
                        # Model-specific API calls
                        if "claude" in model_name:
                            system_prompt = None
                            user_messages = messages.copy()
                            if messages and messages[0]["role"] == "system":
                                system_prompt = messages[0]["content"]
                                user_messages = messages[1:]

                            message = await client.messages.create(
                                model=model_name,
                                max_tokens=max_tokens,
                                system=system_prompt,
                                messages=user_messages,
                                temperature=temperature,
                            )
                        elif (
                            isinstance(client, dict)
                            and client.get("type") == "huggingface"
                        ):
                            # Handle Hugging Face models with optimizations
                            if "multi_gpu_manager" in client:
                                # Use multi-GPU manager for parallel inference
                                multi_gpu_manager = client["multi_gpu_manager"]
                                gpu_info = client.get("gpu_info", {})

                                # Use the tokenizer's chat template if available
                                try:
                                    tokenizer = list(
                                        multi_gpu_manager.tokenizers.values()
                                    )[0]
                                    if "qwen3" in model_name.lower():
                                        if thinking:
                                            prompt = tokenizer.apply_chat_template(
                                                messages,
                                                tokenize=False,
                                                add_generation_prompt=True,
                                                enable_thinking=True,
                                            )
                                        else:
                                            prompt = tokenizer.apply_chat_template(
                                                messages,
                                                tokenize=False,
                                                add_generation_prompt=True,
                                                enable_thinking=False,
                                            )
                                    else:
                                        # Apply the model's chat template
                                        prompt = tokenizer.apply_chat_template(
                                            messages,
                                            tokenize=False,
                                            add_generation_prompt=True,
                                        )
                                except Exception as e:
                                    print(
                                        f"Warning: Could not apply chat template for {model_name}: {e}"
                                    )
                                    print("Falling back to manual formatting...")
                                    # Fallback to manual formatting
                                    prompt = ""
                                    for msg in messages:
                                        role = msg["role"]
                                        content = msg["content"]
                                        if role == "system":
                                            prompt += f"<s>[INST] {content} [/INST]"
                                        elif role == "user":
                                            prompt += f"<s>[INST] {content} [/INST]"
                                        elif role == "assistant":
                                            prompt += f" {content} </s>"

                                    if messages[-1]["role"] == "user":
                                        prompt += " "

                                # Generate using multi-GPU manager
                                loop = asyncio.get_event_loop()

                                def generate_with_multi_gpu():
                                    gpu_id = multi_gpu_manager.get_available_gpu()
                                    generation_kwargs = {
                                        "max_new_tokens": max_tokens,
                                        "temperature": temperature,
                                        "do_sample": temperature > 0,
                                        "top_p": 0.95,
                                        "pad_token_id": tokenizer.pad_token_id
                                        or tokenizer.eos_token_id,
                                        "use_cache": True,
                                        "output_attentions": False,
                                        "output_hidden_states": False,
                                    }

                                    responses = multi_gpu_manager.generate_batch(
                                        [prompt], gpu_id, **generation_kwargs
                                    )
                                    return responses[0] if responses else ""

                                response = await loop.run_in_executor(
                                    None, generate_with_multi_gpu
                                )

                            else:
                                # Single GPU/model inference (existing code)
                                model = client["model"]
                                tokenizer = client["tokenizer"]
                                gpu_info = client.get("gpu_info", {})

                                # Use the tokenizer's chat template if available
                                try:
                                    if "qwen3" in model_name.lower():
                                        if thinking:
                                            prompt = tokenizer.apply_chat_template(
                                                messages,
                                                tokenize=False,
                                                add_generation_prompt=True,
                                                enable_thinking=True,
                                            )
                                        else:
                                            prompt = tokenizer.apply_chat_template(
                                                messages,
                                                tokenize=False,
                                                add_generation_prompt=True,
                                                enable_thinking=False,
                                            )
                                    else:
                                        # Apply the model's chat template
                                        prompt = tokenizer.apply_chat_template(
                                            messages,
                                            tokenize=False,
                                            add_generation_prompt=True,
                                        )
                                except Exception as e:
                                    print(
                                        f"Warning: Could not apply chat template for {model_name}: {e}"
                                    )
                                    print("Falling back to manual formatting...")
                                    # Fallback to manual formatting for models without chat templates
                                    prompt = ""
                                    for msg in messages:
                                        role = msg["role"]
                                        content = msg["content"]
                                        if role == "system":
                                            prompt += f"<s>[INST] {content} [/INST]"
                                        elif role == "user":
                                            prompt += f"<s>[INST] {content} [/INST]"
                                        elif role == "assistant":
                                            prompt += f" {content} </s>"

                                    # If the last message is from user, add the assistant start token
                                    if messages[-1]["role"] == "user":
                                        prompt += " "

                                # Process in event loop to avoid blocking
                            loop = asyncio.get_event_loop()

                            # Tokenize with optimizations
                            def tokenize_prompt():
                                inputs = tokenizer(
                                    prompt,
                                    return_tensors="pt",
                                    padding=True,
                                    truncation=True,
                                    max_length=4096,  # Reasonable max length
                                )

                                # Move to appropriate device
                                if hasattr(model, "device"):
                                    device = model.device
                                elif gpu_info.get("cuda_available", False):
                                    device = torch.device("cuda:0")
                                else:
                                    device = torch.device("cpu")

                                return {k: v.to(device) for k, v in inputs.items()}

                            inputs = await loop.run_in_executor(None, tokenize_prompt)

                            # Generate response with optimizations
                            def generate_response():
                                generation_kwargs = {
                                    **inputs,
                                    "max_new_tokens": max_tokens,
                                    "temperature": temperature,
                                    "do_sample": temperature > 0,
                                    "top_p": 0.95,
                                    "pad_token_id": tokenizer.pad_token_id
                                    or tokenizer.eos_token_id,
                                }

                                # Add memory-efficient generation settings
                                if gpu_info.get("device_count", 0) > 1:
                                    generation_kwargs.update(
                                        {
                                            "use_cache": True,
                                            "output_attentions": False,
                                            "output_hidden_states": False,
                                        }
                                    )

                                with torch.no_grad():  # Ensure no gradients
                                    outputs = model.generate(**generation_kwargs)

                                return outputs

                            outputs = await loop.run_in_executor(
                                None, generate_response
                            )

                            # Decode the response
                            def decode_response():
                                response_text = tokenizer.decode(
                                    outputs[0][inputs["input_ids"].shape[1] :],
                                    skip_special_tokens=True,
                                )
                                return response_text.strip()

                            response = await loop.run_in_executor(None, decode_response)

                            # Clear memory periodically
                            if attempt % 5 == 0:  # Every 5 attempts
                                clear_gpu_memory()
                        else:
                            if (
                                "gpt" in model_name
                                or "o1" in model_name
                                or "o3" in model_name
                            ):
                                # replace "system" with "developer"
                                if messages and messages[0]["role"] == "system":
                                    messages[0]["role"] = "developer"
                            completion = await client.chat.completions.create(
                                model=model_name,
                                messages=messages,
                                max_tokens=max_tokens,
                                temperature=temperature,
                            )

                    # Extract response based on model type
                    if "claude" in model_name:
                        response = message.content[0].text
                    elif (
                        isinstance(client, dict) and client.get("type") == "huggingface"
                    ):
                        # Response is already extracted above
                        pass
                    else:
                        response = completion.choices[0].message.content

                    all_responses.append(response)
                    break

                except Exception as e:
                    if "invalid_prompt" in str(e):
                        print(
                            f"Prompt violated content policy for prompt: {prompt_key}. Replacing with error message."
                        )
                        all_responses.append(
                            "[Error: Prompt violated content policies]"
                        )
                        break
                    elif attempt == max_retries - 1:
                        print(f"Max retries exceeded for prompt: {prompt_key}")
                        print(e)
                        all_responses.append(None)
                    else:
                        print(
                            f"Error occurred for prompt: {prompt_key}. Attempt {attempt + 1}/{max_retries}. Retrying in {retry_delay:.1f} seconds."
                        )
                        print(e)
                        jittered_delay = retry_delay * (
                            1 + jitter * (2 * random.random() - 1)
                        )
                        await asyncio.sleep(jittered_delay)
                        retry_delay *= 2

        results[prompt_key] = all_responses

    tasks = [
        process_prompt(key, messages) for key, messages in formatted_prompts.items()
    ]
    await asyncio.gather(*tasks)

    return results


def create_model_client(
    model_name: str,
    enable_optimizations: bool = True,
    multi_gpu_strategy: str = "auto",
    num_gpus: int = None,
    min_memory_gb: float = 4.0,
):
    """Create optimized model client with enhanced multi-GPU support.

    Args:
        model_name: Name of the model to load
        enable_optimizations: Whether to enable memory and performance optimizations
        multi_gpu_strategy: Strategy for multi-GPU usage:
            - "auto": Automatically choose best strategy based on available GPUs
            - "single": Use single GPU (traditional approach)
            - "parallel": Use multiple model instances across GPUs
            - "pipeline": Use pipeline parallelism (single model across GPUs)
        num_gpus: Number of GPUs to use (None = use all available)
        min_memory_gb: Minimum GPU memory required per GPU
    """
    if "claude" in model_name.lower():
        return AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
    elif (
        "gpt" in model_name.lower()
        or "o1" in model_name.lower()
        or "o3" in model_name.lower()
    ):
        return AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    else:
        print(f"Loading Hugging Face model: {model_name}")

        # Get GPU information
        gpu_info = get_gpu_info()
        print(f"CUDA Available: {gpu_info['cuda_available']}")
        print(f"GPU Count: {gpu_info['device_count']}")
        print(f"CUDA_VISIBLE_DEVICES: {gpu_info['cuda_visible_devices']}")

        if gpu_info["cuda_available"]:
            for device in gpu_info["devices"]:
                print(
                    f"  GPU {device['id']}: {device['name']} ({device['total_memory'] / 1024**3:.1f}GB)"
                )

        # Clear any existing GPU memory
        clear_gpu_memory()

        # Determine multi-GPU strategy
        available_gpus = select_optimal_gpus(num_gpus, min_memory_gb)
        print(f"Available GPUs for use: {available_gpus}")

        if multi_gpu_strategy == "auto":
            if len(available_gpus) > 1:
                # Check if model is large enough to benefit from pipeline parallelism
                model_size_estimate = _estimate_model_size(model_name)
                total_gpu_memory = (
                    sum(gpu_info["devices"][i]["total_memory"] for i in available_gpus)
                    / 1024**3
                )

                if (
                    model_size_estimate > total_gpu_memory * 0.6
                ):  # Model uses >60% of total GPU memory
                    multi_gpu_strategy = "pipeline"
                    print("Auto-selected strategy: pipeline parallelism (large model)")
                else:
                    multi_gpu_strategy = "parallel"
                    print("Auto-selected strategy: parallel instances (smaller model)")
            else:
                multi_gpu_strategy = "single"
                print("Auto-selected strategy: single GPU")

        print(f"Using multi-GPU strategy: {multi_gpu_strategy}")

        # Initialize based on strategy
        if multi_gpu_strategy == "parallel" and len(available_gpus) > 1:
            # Use multiple model instances across GPUs
            print(f"Initializing parallel model instances on GPUs: {available_gpus}")
            multi_gpu_manager = MultiGPUModelManager(
                model_name, available_gpus, enable_optimizations
            )

            return {
                "multi_gpu_manager": multi_gpu_manager,
                "type": "huggingface",
                "gpu_info": gpu_info,
                "strategy": "parallel",
                "available_gpus": available_gpus,
            }

        else:
            # Traditional single model approach (with potential pipeline parallelism)
            print("Loading single model instance...")

            # Load tokenizer
            print("Loading tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(
                model_name, trust_remote_code=True
            )

            # Ensure pad token is set
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token

            # Determine optimal device mapping
            device_map = None
            if (
                enable_optimizations
                and gpu_info["device_count"] > 1
                and multi_gpu_strategy != "single"
            ):
                print("Determining optimal device mapping for pipeline parallelism...")
                device_map = get_optimal_device_map(model_name, gpu_info)
                if device_map:
                    print(f"Using custom device map: {device_map}")
                else:
                    print("Using automatic device mapping")
                    device_map = "auto"
            else:
                if available_gpus:
                    device_map = {"": f"cuda:{available_gpus[0]}"}
                    print(f"Using single GPU: {available_gpus[0]}")
                else:
                    device_map = "auto"

            # Load model with optimizations
            print("Loading model...")
            model_kwargs = {
                "torch_dtype": torch.bfloat16,
                "device_map": device_map,
                "trust_remote_code": True,
            }

            # Add memory optimization flags
            if enable_optimizations:
                model_kwargs.update(
                    {
                        "low_cpu_mem_usage": True,
                        "use_cache": True,
                    }
                )

            model = AutoModelForCausalLM.from_pretrained(model_name, **model_kwargs)

            # Apply additional optimizations
            if enable_optimizations:
                model = setup_model_for_generation(
                    model, enable_memory_optimization=True
                )

            # Log memory usage after loading
            log_gpu_memory_usage("After model loading - ")

            return {
                "model": model,
                "tokenizer": tokenizer,
                "type": "huggingface",
                "gpu_info": gpu_info,
                "device_map": device_map,
                "strategy": multi_gpu_strategy,
                "available_gpus": available_gpus,
            }


def _estimate_model_size(model_name: str) -> float:
    """Estimate model size in GB based on model name."""
    model_name_lower = model_name.lower()

    # Common model size patterns
    size_patterns = {
        "7b": 14.0,
        "8b": 16.0,
        "13b": 26.0,
        "30b": 60.0,
        "70b": 140.0,
        "405b": 810.0,
    }

    for pattern, size_gb in size_patterns.items():
        if pattern in model_name_lower:
            return size_gb

    # Default estimate for unknown models
    return 8.0


async def process_dataframe(
    input_csv,
    output_csv,
    temperature,
    model_name,
    thinking,
    max_tokens,
    lie_k,
    belief_k=None,
    multi_gpu_strategy="auto",
    num_gpus=None,
    min_memory_gb=4.0,
    use_vllm=False,
    vllm_gpus=None,
):
    """
    Process a dataframe and generate responses from an AI model.

    Args:
        input_csv (str): Path to input CSV file
        output_csv (str): Path to output CSV file
        temperature (float): Sampling temperature for generation
        model_name (str): Name of the model to use
        max_tokens (int): Maximum tokens for generation
        belief_k (dict): Required mapping of belief elicitation numbers to number of runs
            e.g. {"1": 3, "2": 1, "3": 1} means run belief_elicit_1 three times,
            belief_elicit_2 and belief_elicit_3 once each
        lie_k (int): Number of lying prompts to generate for each row

    The function reads a CSV containing prompts, generates responses using the specified
    model, and saves the results to a new CSV file.
    """
    # Check if we should use vLLM
    if use_vllm:
        print("Using vLLM backend for inference")

        # Use vLLM data parallelism
        process_dataframe_with_vllm(
            input_csv,
            output_csv,
            model_name,
            temperature,
            max_tokens,
            thinking,
            lie_k,
            belief_k,
            num_gpus=vllm_gpus,
        )
        return  # Exit early since vLLM handles everything

    # Traditional transformers approach
    client = None
    try:
        # Initialize model-specific client with multi-GPU support
        client = create_model_client(
            model_name,
            enable_optimizations=True,
            multi_gpu_strategy=multi_gpu_strategy,
            num_gpus=num_gpus,
            min_memory_gb=min_memory_gb,
        )

        semaphore = asyncio.Semaphore(10)

        # Read input data and set default system prompt if needed
        df = pd.read_csv(input_csv)
        if (
            "belief_system_1" not in df.columns and "belief_elicit_1" in df.columns
        ):  # All files except provided_facts and statistics
            df["belief_system_1"] = "You are a helpful assistant."

        # Check if this is a doubling down scenario (multi-turn conversation)
        is_doubling_down = (
            "dd_turn_1_prompt" in df.columns and "dd_turn_1_response" in df.columns
        )
        print(f"Is this a doubling down scenario? {is_doubling_down}")

        # Prepare prompts for generation
        all_prompts = []
        indices = []
        prompt_types = []

        # Process each row in the dataframe
        print(f"Processing {len(df)} rows from dataframe...")
        for index, row in sync_tqdm(
            df.iterrows(), total=len(df), desc="Processing rows"
        ):
            # Handle lying prompts - either doubling down or standard
            if is_doubling_down:
                # For doubling down, include the full conversation history
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["dd_turn_1_prompt"]},
                            {"role": "assistant", "content": row["dd_turn_1_response"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")
            else:
                # Standard lying prompt with just system and user messages
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")

            # Handle belief elicitation prompts if present
            if "belief_elicit_1" in df.columns:
                for be_num, k_value in belief_k.items():
                    belief_col = f"belief_elicit_{be_num}"
                    if pd.notna(row.get(belief_col)):
                        for run in range(k_value):
                            all_prompts.append(
                                [
                                    {
                                        "role": "system",
                                        "content": row["belief_system_1"],
                                    },
                                    {"role": "user", "content": row[belief_col]},
                                ]
                            )
                            indices.append(index)
                            prompt_types.append(f"be{be_num}_run{run+1}")

        # Generate responses in parallel for all prompts with progress bar
        print(f"Generating responses for {len(all_prompts)} prompts...")

        tasks = [
            generate_responses_async(
                client,
                model_name,
                thinking,
                {prompt_type: prompt},
                max_tokens=max_tokens,
                temperature=temperature,
                semaphore=semaphore,
                K=1,
            )
            for prompt, prompt_type in zip(all_prompts, prompt_types)
        ]

        all_responses = await tqdm.gather(
            *tasks, desc="Generating responses", total=len(tasks)
        )

        # Create mapping for output column names
        column_mapping = {
            f"lying_run{run+1}": f"generation(System Prompt + User Prompt)_run{run+1}"
            for run in range(lie_k)
        }

        # Add belief elicitation column names if present
        if "belief_elicit_1" in df.columns:
            for be_num, k_value in belief_k.items():
                for run in range(k_value):
                    column_mapping[f"be{be_num}_run{run+1}"] = (
                        f"generation(belief_system_1 + belief_elicit_{be_num})_run{run+1}"
                    )

        # Process responses and save to dataframe
        print("Processing responses and saving to dataframe...")
        for response, index, prompt_type in sync_tqdm(
            zip(all_responses, indices, prompt_types),
            total=len(all_responses),
            desc="Processing responses",
        ):
            column_name = column_mapping[prompt_type]
            try:
                response_value = (
                    response[prompt_type][0] if prompt_type in response else None
                )
                if response_value is None:
                    raise ValueError("Generation failed or timed out")
                df.at[index, column_name] = response_value
            except Exception as e:
                error_msg = f"[ERROR: {str(e)}]"
                print(
                    f"WARNING: Error processing response for index {index}, prompt type {prompt_type}: {str(e)}"
                )
                df.at[index, column_name] = error_msg

        # Save results to CSV
        df.to_csv(output_csv, index=False)

    finally:
        # Clean up client if needed
        if hasattr(client, "close"):
            await client.close()
        elif isinstance(client, dict) and "multi_gpu_manager" in client:
            # Clean up multi-GPU manager
            client["multi_gpu_manager"].cleanup()
            print("Multi-GPU manager cleaned up")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate responses from AI models for a set of prompts with GPU optimizations"
    )
    parser.add_argument("--test", action="store_true", help="Run in test mode")
    parser.add_argument(
        "--model",
        default="meta-llama/Llama-3.1-8B-Instruct",
        help="Model name to use for generation",
    )
    parser.add_argument(
        "--thinking",
        action="store_true",
        help="Enable thinking mode if applicable",
    )
    parser.add_argument(
        "--max_tokens", type=int, default=1000, help="Maximum tokens for generation"
    )
    parser.add_argument(
        "--temperature", type=float, default=1.0, help="Temperature for generation"
    )
    parser.add_argument(
        "--lie_k",
        type=int,
        default=1,
        help="Number of lying prompts to generate for each row",
    )
    parser.add_argument(
        "--disable_optimizations",
        action="store_true",
        help="Disable GPU memory optimizations and multi-GPU features",
    )
    parser.add_argument(
        "--memory_monitoring",
        action="store_true",
        help="Enable detailed GPU memory monitoring during generation",
    )
    parser.add_argument(
        "--batch_size",
        type=int,
        default=1,
        help="Batch size for generation (experimental, use with caution)",
    )
    parser.add_argument(
        "--multi_gpu_strategy",
        type=str,
        default="auto",
        choices=["auto", "single", "parallel", "pipeline"],
        help="Multi-GPU strategy: auto (choose automatically), single (use one GPU), "
        "parallel (multiple model instances), pipeline (model across GPUs)",
    )
    parser.add_argument(
        "--num_gpus",
        type=int,
        default=None,
        help="Number of GPUs to use (default: use all available)",
    )
    parser.add_argument(
        "--min_memory_gb",
        type=float,
        default=4.0,
        help="Minimum GPU memory required per GPU in GB",
    )
    parser.add_argument(
        "--gpu_ids",
        type=str,
        default=None,
        help="Comma-separated list of GPU IDs to use (e.g., '0,1,2')",
    )
    parser.add_argument(
        "--use_vllm",
        action="store_true",
        help="Use vLLM backend for inference (requires vllm package)",
    )
    parser.add_argument(
        "--vllm_gpus",
        type=int,
        default=None,
        help="Number of GPUs to use for vLLM data parallelism (default: all available)",
    )

    args = parser.parse_args()

    # Handle GPU ID specification
    if args.gpu_ids:
        os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu_ids
        print(f"Set CUDA_VISIBLE_DEVICES to: {args.gpu_ids}")

    # Print optimization settings
    print("=== GPU Optimization Settings ===")
    print(f"Backend: {'vLLM' if args.use_vllm else 'Transformers'}")
    if args.use_vllm:
        print(f"vLLM GPUs: {args.vllm_gpus or 'all available'}")
        if not VLLM_AVAILABLE:
            print("WARNING: vLLM requested but not available!")
    else:
        print(f"Optimizations enabled: {not args.disable_optimizations}")
        print(f"Memory monitoring: {args.memory_monitoring}")
        print(f"Batch size: {args.batch_size}")
        print(f"Multi-GPU strategy: {args.multi_gpu_strategy}")
        print(f"Number of GPUs: {args.num_gpus or 'auto'}")
        print(f"Min memory per GPU: {args.min_memory_gb}GB")
    if args.gpu_ids:
        print(f"GPU IDs: {args.gpu_ids}")
    print("=" * 35)

    for file in glob.iglob("test_csv_data/*.csv" if args.test else "csv_data/*.csv"):
        belief_k = {"1": 3} if "statistics" in file else {"1": 3, "2": 1, "3": 1}

        modelname = args.model.split("/")[-1] if "/" in args.model else args.model
        output_file = file.replace(".csv", f"_{modelname}.csv")
        output_file = output_file.replace("csv_data/", "csv_data/responses/")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Skip if output file already exists
        if os.path.exists(output_file):
            print(f"Skipping {output_file} as it already exists")
            continue

        print(f"\nProcessing {file} -> {output_file}")

        # Store optimization settings globally for access in other functions
        global ENABLE_OPTIMIZATIONS, MEMORY_MONITORING, BATCH_SIZE
        ENABLE_OPTIMIZATIONS = not args.disable_optimizations
        MEMORY_MONITORING = args.memory_monitoring
        BATCH_SIZE = args.batch_size

        asyncio.run(
            process_dataframe(
                file,
                output_file,
                temperature=args.temperature,
                model_name=args.model,
                thinking=args.thinking,
                max_tokens=args.max_tokens,
                lie_k=args.lie_k,
                belief_k=belief_k,
                multi_gpu_strategy=args.multi_gpu_strategy,
                num_gpus=args.num_gpus,
                min_memory_gb=args.min_memory_gb,
                use_vllm=args.use_vllm,
                vllm_gpus=args.vllm_gpus,
            )
        )

        # Clear memory between files
        if ENABLE_OPTIMIZATIONS:
            clear_gpu_memory()
            print("Cleared GPU memory between files")
